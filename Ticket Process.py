# -*- coding: utf-8 -*-
import sys
import os
import json
import pickle
import atexit

# Ensure UTF-8 encoding for all text operations
if sys.version_info[0] >= 3:
    try:
        import io
        if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.buffer:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        pass  # Skip if already configured or not available

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.core.text import LabelBase
from kivy.effects.scroll import ScrollEffect
import pyodbc
import datetime
import getpass

import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
import time
import unicodedata
import pyperclip

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\Ticketing Process.accdb"

# Field headers
fields = [
    "Fresh/Backlog", "Ticket Owner", "Ticket ID", "Title Description", "Queue", "Created Date",
    "Last Assigned", "Age", "DF Age", "Action Date",
    "Business Line", "Partner", "Market Place", "Identifiers(GTI/VCID/ASIN)", "Title", "Content Type", "Action Taken", "Group Assigned", "Single/Double", "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2", "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3", "Titles Received", "Titles Worked",
    "Comments"
    ]

# Dropdown options
BUSINESS_LINE_OPTIONS = ["EST", "VOD", "3p Subs", "SVOD", "N/A"]

CONTENT_TYPE_OPTIONS = ["Movie", "Series", "N/A"]

GROUP_ASSIGNED_OPTIONS = [
    "Encoding", "Redelivery Group", "DFSubtitleFix", "Support engineering", "Trust&Safety",
    "Merge", "Amazon Studios", "CAM", "POM", "Offers", "PVD", "Ownership",
    "Asset Management", "Internal DF team", "N/A"
]

TICKET_STATUS_OPTIONS = [
    "Resolved", "Reassigned", "Pending Verification of Fix", "Pending of DF -Wip",
    "Reached out (Requestor Team) for Addicional Info/Clarity", "Follow Up"
]

ISSUE_TYPE_OPTIONS = [
    "Mapping", "Ratings", "VCID", "Merge Issue", "Artwork", "Incorrect Content",
    "Playback", "Encoding", "Content Descriptor", "Availability", "Subtitle",
    "Audio", "Video", "Metadata", "Offer", "Assets", "Avails", "Duplicate", "N/A"
]

ISSUE_SUBTYPE_OPTIONS = [
    "Artwork - Incorrect Artwork", "Artwork - Missing", "Assets", "Assets - Inconsistent within Epsiodes",
    "Assets - Missing", "Audio - Duration difference", "Audio - Incorrect Audio", "Audio - Missing",
    "Audio - Out of sync", "Audio - Quality", "Availability - Content unavailable due to tech error",
    "Avails - Holdback issues", "Avails - Incorrect avails delivered", "Avails - Not delivered",
    "Avails - Pricing Issue", "Encoding Issue", "Merge Issue", "Metadata - Missing",
    "Metadata - Quality issue (incorrect details)", "Metadata - Ratings/ Content descriptors",
    "Metadata - Sequencing issue", "Metadata - Synopsis issue", "Offer - Take down / Revoke request",
    "Offers - Expired offers", "Offers - Future offers", "Offers - In conflict",
    "Offers - Inconsistence", "Offers - Not created", "Offers - Pricing missmatch",
    "Playback - Video error in site", "POM matrix updating request", "Request - Mapping",
    "Subtitle - Incorrect Subtitle", "Subtitle - Missing", "Subtitle - Out of sync",
    "Subtitle - Quality", "Video - Incorrect Content", "Video - Missing", "Video - Out of VC spec",
    "Video - Quality", "Availability - Content unavailable due to internal error",
    "Trailer - Playback", "Trailer - Missing", "N/A"
]

class TicketingForm(MDApp):
    def __init__(self):
        super().__init__()
        self.entries = {}
        self.selected_issues = []
        self.selected_rca_items = []
        self.offer_type_menu = None
        self.user_dropdown = None

        # Dropdown menus for various fields
        self.business_line_menu = None
        self.content_type_menu = None
        self.group_assigned_menu = None
        self.ticket_status_menu = None
        self.issue_type_menus = {}  # For Issue Type 1, 2, 3
        self.issue_subtype_menus = {}  # For Issue Subtype 1, 2, 3
        self.edp_link_url = ""
        self.current_gti_index = 0
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        self.lob_menu = None
        self.code_expansion_menu = None
        self.action_menu = None
        self.resolver_menu = None
        self.start_time = None
        self.end_time = None
        self.break_start_time = None  # datetime.time object for tracking breaks internally
        self.break_end_time = None    # datetime.time object for tracking breaks internally
        self.total_break_time = 0.0   # Total break time in minutes (float)
        self.break_duration_db = 0.0   # Break duration saved to database
        self.issue_name = ""  # Store the button name separately
        self.cumulative_time = 0.0  # Store the cumulative total time for display
        self.titles_processed = 0  # Counter for completed/submitted titles
        self.autosave_path = os.path.join(os.path.expanduser("~"), ".tv_series_tool_autosave.pkl")
        
        # Season/Episode data for View Details functionality
        self.current_season_episode_data = ""
        self.current_territory_data = ""

        # Get Task functionality
        self.task_allocation_data = []
        self.current_task_data = None
        self.ticket_owner_dropdown = None
        self.processed_ticket_ids = set()  # Track unique processed Ticket IDs
        
        # Font setup will be done in build() method after Kivy initialization

        # Initialize cursor settings
        self.setup_cursor_management()

        # Register auto-save on exit
        atexit.register(self.auto_save_data)

        # Try to load previous session data
        self.try_load_autosave()
    
    def setup_unicode_support(self):
        """Setup Unicode font support for international characters"""
        try:
            # Register fonts that support Japanese characters
            unicode_fonts = [
                'C:/Windows/Fonts/msgothic.ttc',  # MS Gothic - supports Japanese
                'C:/Windows/Fonts/msmincho.ttc',  # MS Mincho - supports Japanese
                'C:/Windows/Fonts/meiryo.ttc',    # Meiryo - supports Japanese
                'C:/Windows/Fonts/arial.ttf',     # Arial Unicode
                'C:/Windows/Fonts/calibri.ttf',   # Calibri
                'C:/Windows/Fonts/segoeui.ttf'    # Segoe UI
            ]

            print("Checking for Unicode fonts...")
            font_registered = False
            for font_path in unicode_fonts:
                print(f"Checking font: {font_path}")
                if os.path.exists(font_path):
                    try:
                        LabelBase.register(name='UnicodeFont', fn_regular=font_path)
                        print(f"Successfully registered Unicode font: {font_path}")
                        font_registered = True
                        break
                    except Exception as font_error:
                        print(f"Failed to register font {font_path}: {font_error}")
                        continue
                else:
                    print(f"Font not found: {font_path}")

            if not font_registered:
                print("Warning: No Unicode-compatible font found, using default font")
                # Try to register a fallback font
                try:
                    LabelBase.register(name='UnicodeFont', fn_regular=None)  # Use default
                    print("Registered default font as UnicodeFont")
                except Exception as fallback_error:
                    print(f"Failed to register fallback font: {fallback_error}")

        except Exception as e:
            print(f"Font registration error: {e}")

    def setup_cursor_management(self):
        """Setup cursor management to prevent cursor disappearing"""
        try:
            # Force cursor to be visible
            from kivy.core.window import Window
            Window.show_cursor = True

            # Set default cursor
            try:
                Window.set_system_cursor('arrow')
            except:
                # Fallback if system cursor setting fails
                pass

            print("Cursor management initialized")
        except Exception as e:
            print(f"Cursor management warning: {e}")

    def normalize_unicode_text(self, text):
        """Normalize Unicode text to handle international characters properly"""
        if not text:
            return ""
        try:
            # Convert to string first
            text_str = str(text)
            # Normalize Unicode text to NFC form for consistent character representation
            normalized = unicodedata.normalize('NFC', text_str)
            # Ensure proper encoding for web forms
            encoded = normalized.encode('utf-8').decode('utf-8')
            return encoded
        except Exception as e:
            print(f"Unicode normalization error: {e}")
            # Fallback to basic string conversion
            try:
                return str(text).encode('utf-8', errors='replace').decode('utf-8')
            except:
                return str(text)

    def keyboard_handler(self, window, key, *args):
        # Prevent Escape key from closing the application
        if key == 27:  # 27 is the keycode for Escape key
            return True  # Return True to indicate the key was handled
        return False  # Let other keys pass through

    def on_cursor_enter(self, window, *args):
        """Handle cursor entering the window"""
        Window.show_cursor = True
        Window.set_system_cursor('arrow')
        return True

    def on_cursor_leave(self, window, *args):
        """Handle cursor leaving the window"""
        Window.show_cursor = True
        return True

    def on_mouse_motion(self, window, pos, *args):
        """Handle mouse motion to ensure cursor remains visible"""
        Window.show_cursor = True
        return False  # Allow other handlers to process the motion event

    def check_cursor_visibility(self, dt):
        """Periodic check to ensure cursor remains visible"""
        try:
            Window.show_cursor = True
            # Optionally reset cursor type if needed
            if hasattr(Window, 'set_system_cursor'):
                try:
                    Window.set_system_cursor('arrow')
                except:
                    pass
        except Exception as e:
            print(f"Cursor visibility check error: {e}")
        return True  # Continue scheduling
        
    def build(self):
        # Configure font for Unicode support first
        self.setup_unicode_support()

        Window.maximize()
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"

        # Ensure cursor remains visible
        Window.show_cursor = True
        Window.set_system_cursor('arrow')

        # Bind keyboard to prevent Escape key from closing the app
        Window.bind(on_keyboard=self.keyboard_handler)

        # Bind mouse events to ensure cursor visibility
        Window.bind(on_cursor_enter=self.on_cursor_enter)
        Window.bind(on_cursor_leave=self.on_cursor_leave)
        Window.bind(on_motion=self.on_mouse_motion)

        # Schedule periodic cursor check
        from kivy.clock import Clock
        Clock.schedule_interval(self.check_cursor_visibility, 1.0)  # Check every second
        
        # Main layout
        main_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(20), md_bg_color="dodgerblue")
        
        # Header with title and buttons with gaps
        header = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))
        title = MDLabel(text="Ticketing Task Form:", theme_text_color="Primary", 
                       font_style="H5", size_hint_x=0.20, bold=True)
        
        # Total Time display with formatted value
        self.time_display = MDLabel(text="Total Time: 0.00 min", theme_text_color="Secondary",
                                  font_style="Subtitle1", size_hint_x=0.10, bold=True)
        
        # Titles Processed display
        self.titles_display = MDLabel(text="Titles Processed: 0", theme_text_color="Secondary",
                                   font_style="Subtitle1", size_hint_x=0.10, bold=True)
        
        get_task_btn = MDRaisedButton(text="Get Task", size_hint_x=0.07, md_bg_color="purple",
                                     on_release=self.get_task)
        gap1 = MDLabel(text="", size_hint_x=0.01)

        break_btn = MDRaisedButton(text="Pause", size_hint_x=0.07, md_bg_color="orange",
                                  on_release=self.set_break_mode)
        gap4 = MDLabel(text="", size_hint_x=0.01)

        active_btn = MDRaisedButton(text="Play", size_hint_x=0.07, md_bg_color="green",
                                   on_release=self.set_active_mode)
        gap5 = MDLabel(text="", size_hint_x=0.01)

        submit_btn = MDRaisedButton(text="Submit", size_hint_x=0.07,
                                   on_release=self.submit_task)

        header.add_widget(title)
        header.add_widget(self.time_display)
        header.add_widget(self.titles_display)
        header.add_widget(get_task_btn)
        header.add_widget(gap1)
        header.add_widget(break_btn)
        header.add_widget(gap4)
        header.add_widget(active_btn)
        header.add_widget(gap5)
        header.add_widget(submit_btn)
        main_layout.add_widget(header)
        
        # Scrollable content with proper configuration
        scroll = MDScrollView(
            do_scroll_x=False,
            do_scroll_y=True,
            scroll_type=['content'],
            bar_width=dp(10),
            effect_cls=ScrollEffect  # Use basic scroll effect instead of damped
        )
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), 
                             adaptive_height=True, padding=dp(10),
                             size_hint_y=None)
        content.bind(minimum_height=content.setter('height'))
        
        # Form fields
        form_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        form_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        for field in fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1", )
            
            if field in ["Offer Type", "Business Line", "Content Type", "Group Assigned", "Ticket Status",
                         "Issue Type 1", "Issue Subtype 1", "Issue Type 2", "Issue Subtype 2",
                         "Issue Type 3", "Issue Subtype 3"]:
                # Dropdown fields
                field_layout.add_widget(label)
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)

                # Create dropdown button with appropriate text
                if field == "Offer Type":
                    button_text = "Select Offer Type"
                elif field == "Business Line":
                    button_text = "Select Business Line"
                elif field == "Content Type":
                    button_text = "Select Content Type"
                elif field == "Group Assigned":
                    button_text = "Select Group Assigned"
                elif field == "Ticket Status":
                    button_text = "Select Ticket Status"
                elif "Issue Type" in field:
                    button_text = f"Select {field}"
                elif "Issue Subtype" in field:
                    button_text = f"Select {field}"
                else:
                    button_text = f"Select {field}"

                dropdown_field = MDRaisedButton(text=button_text, size_hint_x=0.3,
                                              on_release=lambda x, f=field: self.open_dropdown_menu(x, f))
                self.entries[field] = dropdown_field
                field_layout.add_widget(dropdown_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            else:
                # All text fields are now multiline with Unicode support
                field_layout.add_widget(label)
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=4000, font_name='UnicodeFont')
                text_field.height = dp(120)
                text_field.bind(on_key_down=self.on_key_down)
                text_field.bind(text=self.on_text_change)  # Add Unicode handling
                text_field.bind(focus=self.on_text_field_focus)  # Ensure cursor visibility on focus
                if field == "Season/Episode Number":
                    text_field.bind(on_text_validate=self.format_season_episode)
                    text_field.bind(focus=self.on_season_episode_focus)
                self.entries[field] = text_field
                field_layout.add_widget(text_field)
            
            field_layout.height = dp(120)  # All fields are now multiline
            form_layout.add_widget(field_layout)
        

        
        # Fields Section
        form_card.add_widget(form_layout)
        content.add_widget(form_card)
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)

        return main_layout

    def on_text_change(self, instance, text):
        """Handle text changes with Unicode normalization"""
        try:
            normalized_text = self.normalize_unicode_text(text)
            if normalized_text != text:
                instance.text = normalized_text

            # Auto-save after significant text changes
            # Use a simple counter to avoid saving too frequently
            if not hasattr(self, '_text_change_counter'):
                self._text_change_counter = 0

            self._text_change_counter += 1
            if self._text_change_counter >= 20:  # Save every 20 text changes
                self.auto_save_data()
                self._text_change_counter = 0
        except Exception as e:
            print(f"Text normalization error: {e}")

    def open_offer_menu_click(self, instance):
        if not hasattr(self, 'selected_offers'):
            self.selected_offers = set()

        menu_items = [
            {"text": "Prime", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Prime")},
            {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("TVOD")},
            {"text": "Channels", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Channels")},
            {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("FVOD")},
            {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("AVOD")},
            {"text": "POEST", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("POEST")},
            {"text": "SVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("SVOD")},
        ]
        self.offer_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.offer_type_menu.open()

    def toggle_offer_type(self, value):
        if value in self.selected_offers:
            self.selected_offers.remove(value)
        else:
            self.selected_offers.add(value)

        # Update button text with selected offers
        if self.selected_offers:
            self.entries["Offer Type"].text = ", ".join(sorted(self.selected_offers))
        else:
            self.entries["Offer Type"].text = "Select Offer Type"

    def open_dropdown_menu(self, instance, field):
        """Open dropdown menu for various fields"""
        if field == "Offer Type":
            self.open_offer_menu_click(instance)
        elif field == "Business Line":
            self.open_business_line_menu(instance)
        elif field == "Content Type":
            self.open_content_type_menu(instance)
        elif field == "Group Assigned":
            self.open_group_assigned_menu(instance)
        elif field == "Ticket Status":
            self.open_ticket_status_menu(instance)
        elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
            self.open_issue_type_menu(instance, field)
        elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
            self.open_issue_subtype_menu(instance, field)

    def open_business_line_menu(self, instance):
        """Open Business Line dropdown menu"""
        menu_items = []
        for option in BUSINESS_LINE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Business Line")
            })
        self.business_line_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.business_line_menu.open()

    def open_content_type_menu(self, instance):
        """Open Content Type dropdown menu"""
        menu_items = []
        for option in CONTENT_TYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Content Type")
            })
        self.content_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.content_type_menu.open()

    def open_group_assigned_menu(self, instance):
        """Open Group Assigned dropdown menu"""
        menu_items = []
        for option in GROUP_ASSIGNED_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Group Assigned")
            })
        self.group_assigned_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.group_assigned_menu.open()

    def open_ticket_status_menu(self, instance):
        """Open Ticket Status dropdown menu"""
        menu_items = []
        for option in TICKET_STATUS_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, "Ticket Status")
            })
        self.ticket_status_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.ticket_status_menu.open()

    def open_issue_type_menu(self, instance, field):
        """Open Issue Type dropdown menu"""
        menu_items = []
        for option in ISSUE_TYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, field)
            })
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.issue_type_menus[field] = menu
        menu.open()

    def open_issue_subtype_menu(self, instance, field):
        """Open Issue Subtype dropdown menu"""
        menu_items = []
        for option in ISSUE_SUBTYPE_OPTIONS:
            menu_items.append({
                "text": option,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=option: self.set_dropdown_value(instance, x, field)
            })
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.issue_subtype_menus[field] = menu
        menu.open()

    def set_dropdown_value(self, button, value, field):
        """Set dropdown value and dismiss menu"""
        button.text = value

        # Dismiss the appropriate menu
        if field == "Business Line" and self.business_line_menu:
            self.business_line_menu.dismiss()
        elif field == "Content Type" and self.content_type_menu:
            self.content_type_menu.dismiss()
        elif field == "Group Assigned" and self.group_assigned_menu:
            self.group_assigned_menu.dismiss()
        elif field == "Ticket Status" and self.ticket_status_menu:
            self.ticket_status_menu.dismiss()
        elif field in self.issue_type_menus:
            self.issue_type_menus[field].dismiss()
        elif field in self.issue_subtype_menus:
            self.issue_subtype_menus[field].dismiss()

    def get_task(self, instance):
        """Load task allocation data and show ticket owner selection popup or load autosaved data by Ticket ID"""
        try:
            # First check if user has entered a Ticket ID to load autosaved data
            ticket_id_field = self.entries.get("Ticket ID")
            if ticket_id_field and hasattr(ticket_id_field, 'text') and ticket_id_field.text.strip():
                ticket_id = ticket_id_field.text.strip()
                if self.load_autosaved_data_by_ticket_id(ticket_id):
                    return  # Successfully loaded autosaved data, no need to proceed further

            # Connect to database and fetch data from Ticketing Task Allocation table
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            # Fetch all data from Ticketing Task Allocation table
            cursor.execute("SELECT * FROM [Ticketing Task Allocation]")
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            self.task_allocation_data = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[columns[i]] = value
                self.task_allocation_data.append(row_dict)

            conn.close()

            if not self.task_allocation_data:
                dialog = MDDialog(title="No Data", text="No tasks found in Ticketing Task Allocation table.")
                dialog.open()
                return

            # Get unique ticket owners
            ticket_owners = list(set([row.get('Ticket Owner', '') for row in self.task_allocation_data if row.get('Ticket Owner', '')]))
            ticket_owners.sort()

            if not ticket_owners:
                dialog = MDDialog(title="No Data", text="No Ticket Owners found in the data.")
                dialog.open()
                return

            # Show ticket owner selection popup
            self.show_ticket_owner_selection(ticket_owners)

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to load task data: {str(e)}")
            dialog.open()

    def show_ticket_owner_selection(self, ticket_owners):
        """Show popup with dropdown for ticket owner selection"""
        # Create content layout
        content_layout = MDBoxLayout(orientation="vertical", spacing=dp(20),
                                   adaptive_height=True, size_hint_y=None, padding=dp(20))
        content_layout.bind(minimum_height=content_layout.setter('height'))

        # Add instruction label
        instruction_label = MDLabel(text="Select a Ticket Owner to load tasks:",
                                  theme_text_color="Primary", font_style="Subtitle1",
                                  size_hint_y=None, height=dp(40))
        content_layout.add_widget(instruction_label)

        # Create dropdown button
        self.ticket_owner_btn = MDRaisedButton(text="Select Ticket Owner",
                                             size_hint_y=None, height=dp(50),
                                             on_release=lambda x: self.open_ticket_owner_menu(ticket_owners))
        content_layout.add_widget(self.ticket_owner_btn)

        # Create dialog
        self.ticket_owner_dialog = MDDialog(
            title="Get Task",
            type="custom",
            content_cls=content_layout,
            size_hint=(0.8, 0.6),
            buttons=[
                MDFlatButton(text="Cancel", on_release=self.close_ticket_owner_dialog),
                MDRaisedButton(text="Load Tasks", on_release=self.load_selected_tasks)
            ]
        )
        self.ticket_owner_dialog.open()

    def open_ticket_owner_menu(self, ticket_owners):
        """Open dropdown menu for ticket owner selection"""
        menu_items = []
        for owner in ticket_owners:
            menu_items.append({
                "text": owner,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=owner: self.select_ticket_owner(x)
            })

        self.ticket_owner_dropdown = MDDropdownMenu(
            caller=self.ticket_owner_btn,
            items=menu_items,
            width_mult=4
        )
        self.ticket_owner_dropdown.open()

    def select_ticket_owner(self, owner):
        """Handle ticket owner selection"""
        self.ticket_owner_btn.text = owner
        self.selected_ticket_owner = owner
        self.ticket_owner_dropdown.dismiss()

    def close_ticket_owner_dialog(self, instance):
        """Close ticket owner selection dialog"""
        self.ticket_owner_dialog.dismiss()

    def load_selected_tasks(self, instance):
        """Load tasks for selected ticket owner and populate form"""
        if not hasattr(self, 'selected_ticket_owner'):
            dialog = MDDialog(title="Error", text="Please select a Ticket Owner first.")
            dialog.open()
            return

        # Filter tasks for selected ticket owner
        owner_tasks = [task for task in self.task_allocation_data
                      if task.get('Ticket Owner', '') == self.selected_ticket_owner]

        if not owner_tasks:
            dialog = MDDialog(title="No Tasks", text=f"No tasks found for {self.selected_ticket_owner}.")
            dialog.open()
            return

        # Close the selection dialog
        self.ticket_owner_dialog.dismiss()

        # Populate form with first task and set up for cycling through tasks
        self.current_task_index = 0
        self.owner_tasks = owner_tasks
        self.populate_form_with_task(owner_tasks[0])

        # Update titles processed count based on unique Ticket IDs
        unique_ticket_ids = set([task.get('Ticket ID', '') for task in owner_tasks if task.get('Ticket ID', '')])
        self.titles_processed = len(unique_ticket_ids)
        self.titles_display.text = f"Titles Processed: {self.titles_processed}"

        dialog = MDDialog(title="Success", text=f"Loaded {len(owner_tasks)} tasks for {self.selected_ticket_owner}")
        dialog.open()

    def populate_form_with_task(self, task_data):
        """Populate form fields with task data"""
        try:
            # Map task data to form fields
            for field in fields:
                if field in task_data and task_data[field] is not None:
                    value = str(task_data[field])

                    if field in ["Offer Type", "Business Line", "Content Type", "Group Assigned",
                               "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2",
                               "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3"]:
                        # Handle dropdown fields
                        if value and value != "None":
                            self.entries[field].text = value
                        else:
                            # Reset to default text if no value
                            if field == "Offer Type":
                                self.entries[field].text = "Select Offer Type"
                            elif field == "Business Line":
                                self.entries[field].text = "Select Business Line"
                            elif field == "Content Type":
                                self.entries[field].text = "Select Content Type"
                            elif field == "Group Assigned":
                                self.entries[field].text = "Select Group Assigned"
                            elif field == "Ticket Status":
                                self.entries[field].text = "Select Ticket Status"
                            elif "Issue Type" in field:
                                self.entries[field].text = f"Select {field}"
                            elif "Issue Subtype" in field:
                                self.entries[field].text = f"Select {field}"
                    else:
                        # Handle text fields
                        if hasattr(self.entries[field], 'text'):
                            self.entries[field].text = value

            # Store current task data
            self.current_task_data = task_data

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to populate form: {str(e)}")
            dialog.open()



    def format_season_episode(self, instance):
        import re
        text = instance.text
        # Pattern to match S##-(episode_numbers) format
        formatted_text = re.sub(r'(S\d+)-(\([^)]+\))', r'\1-E\2', text)
        if formatted_text != text:
            instance.text = formatted_text

    def on_text_field_focus(self, instance, focus):
        """Handle text field focus to ensure cursor visibility"""
        if focus:
            # Ensure cursor is visible when text field gets focus
            Window.show_cursor = True
            try:
                Window.set_system_cursor('ibeam')  # Text cursor
            except:
                Window.set_system_cursor('arrow')  # Fallback to arrow
        else:
            # Reset to arrow cursor when losing focus
            try:
                Window.set_system_cursor('arrow')
            except:
                pass

    def on_season_episode_focus(self, instance, focus):
        # Call the general focus handler first
        self.on_text_field_focus(instance, focus)

        if not focus:  # When field loses focus
            self.format_season_episode(instance)

    def on_key_down(self, instance, keycode, text, modifiers):
        if keycode == 9:  # Tab key
            # Exclude all dropdown fields from tab navigation
            dropdown_fields = ["Offer Type", "Business Line", "Content Type", "Group Assigned",
                             "Ticket Status", "Issue Type 1", "Issue Subtype 1", "Issue Type 2",
                             "Issue Subtype 2", "Issue Type 3", "Issue Subtype 3"]
            field_names = [f for f in fields if f not in dropdown_fields]
            current_field = None
            for field, entry in self.entries.items():
                if entry == instance:
                    current_field = field
                    break
            if current_field and current_field in field_names:
                current_index = field_names.index(current_field)
                next_index = (current_index + 1) % len(field_names)
                next_field = self.entries[field_names[next_index]]
                next_field.focus = True
            return True
        return False

    def submit_data(self, data):
        user_id = getpass.getuser()
        today = datetime.date.today()
        week_num = today.isocalendar()[1]

        try:
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            insert_query = f"""
            INSERT INTO [Ticketing Process] (
                [Fresh/Backlog], [Ticket Owner], [Ticket ID], [Title Description], [Queue], [Created Date], 
                [Last Assigned], [Age], [DF Age], [Action Date],
                [Business Line], [Partner], [Market Place], [Identifiers(GTI/VCID/ASIN)], [Title], [Content Type], [Action Taken], [Group Assigned], [Single/Double], [Ticket Status], [Issue Type 1], [Issue Subtype 1], [Issue Type 2], [Issue Subtype 2], [Issue Type 3], [Issue Subtype 3], [Titles Received], [Titles Worked],
                [Comments], [Date], [Week], [User ID], [Start Time], [End Time], [Total Time],
                [Break Total Time]
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            self.end_time = datetime.datetime.now().time()

            # Calculate Total Time in minutes excluding break time
            if self.start_time:
                start_datetime = datetime.datetime.combine(today, self.start_time)
                end_datetime = datetime.datetime.combine(today, self.end_time)

                # Handle case where task spans midnight
                if end_datetime < start_datetime:
                    end_datetime = end_datetime + datetime.timedelta(days=1)

                gross_time = (end_datetime - start_datetime).total_seconds() / 60

                # Add any current break time if still in break mode
                if self.break_start_time:
                    current_break_time = datetime.datetime.now().time()
                    break_start_dt = datetime.datetime.combine(today, self.break_start_time)
                    current_break_dt = datetime.datetime.combine(today, current_break_time)

                    # Handle case where current break spans midnight
                    if current_break_dt < break_start_dt:
                        current_break_dt = current_break_dt + datetime.timedelta(days=1)

                    current_break_duration = (current_break_dt - break_start_dt).total_seconds() / 60
                    self.total_break_time += current_break_duration
                    # Reset break start time since we've accounted for it
                    self.break_start_time = None

                # Net time is gross time minus break time
                net_time = gross_time - self.total_break_time
                if net_time < 0:
                    net_time = 0  # Ensure we don't have negative time

                total_time = round(net_time, 2)
            else:
                # If no start time set, use current time as both start and end
                self.start_time = self.end_time
                total_time = 0.0

            # Create data list with proper field mapping and data type handling
            def get_field_value(field_name):
                # Handle all dropdown fields
                field_value = data.get(field_name, "")

                # Check if this is a dropdown field with default text
                default_texts = {
                    "Offer Type": "Select Offer Type",
                    "Business Line": "Select Business Line",
                    "Content Type": "Select Content Type",
                    "Group Assigned": "Select Group Assigned",
                    "Ticket Status": "Select Ticket Status",
                    "Issue Type 1": "Select Issue Type 1",
                    "Issue Subtype 1": "Select Issue Subtype 1",
                    "Issue Type 2": "Select Issue Type 2",
                    "Issue Subtype 2": "Select Issue Subtype 2",
                    "Issue Type 3": "Select Issue Type 3",
                    "Issue Subtype 3": "Select Issue Subtype 3"
                }

                # If field has default text, return empty string
                if field_name in default_texts and field_value == default_texts[field_name]:
                    return ""

                return field_value

            # Prepare data for insertion
            data_values = []
            for field in fields:
                value = get_field_value(field)
                # Ensure all values are strings and handle None values
                if value is None:
                    value = ""
                data_values.append(str(value))

            # Add empty values for Series GTI and Series Name since we removed task section
            data_values.extend(["", ""])  # Series GTI, Series Name

            # Add date/time info and break time
            final_data = []
            final_data.extend(data_values)
            final_data.extend([today, week_num, str(user_id), self.start_time, self.end_time, total_time])

            # Add only break total time to the final data list
            break_duration_value = getattr(self, 'break_duration_db', 0.0)
            formatted_break_duration = f"{break_duration_value:.2f}"  # Format as string with 2 decimal places

            # Only add Break Total Time to database
            final_data.append(formatted_break_duration)  # Break Total Time as string with 2 decimal places

            cursor.execute(insert_query, final_data)
            conn.commit()
            conn.close()

            # Update cumulative time and titles processed
            self.cumulative_time += total_time

            # Count titles processed based on unique Ticket IDs
            ticket_id = data.get("Ticket ID", "").strip()
            if ticket_id:
                # Initialize processed ticket IDs set if not exists
                if not hasattr(self, 'processed_ticket_ids'):
                    self.processed_ticket_ids = set()

                # Add current ticket ID to processed set
                self.processed_ticket_ids.add(ticket_id)
                self.titles_processed = len(self.processed_ticket_ids)
            else:
                # If no Ticket ID, increment by 1 (fallback behavior)
                self.titles_processed += 1

            # Update displays
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
            self.titles_display.text = f"Titles Processed: {self.titles_processed}"

            # Clear form after successful submission
            self.clear_form()

            # Clean up ticket-specific autosave file after successful submission
            if ticket_id:
                self.cleanup_ticket_autosave(ticket_id)

            # Reset timing for next task
            self.start_time = None
            self.end_time = None
            self.total_break_time = 0.0
            self.break_duration_db = 0.0

            dialog = MDDialog(title="Success", text="Data submitted successfully!")
            dialog.open()

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to submit data: {str(e)}")
            dialog.open()

    def preview_data(self, instance):
        data = {field: self.entries[field].text for field in fields}

        preview_text = "Data Preview:\n\n"
        for field, value in data.items():
            preview_text += f"{field}: {value}\n"

        preview_dialog = MDDialog(
            title="Data Preview",
            text=preview_text,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: preview_dialog.dismiss()),
                MDRaisedButton(text="Submit", on_release=lambda x: [preview_dialog.dismiss(), self.submit_data(data)])
            ]
        )
        preview_dialog.open()







    def set_break_mode(self, instance):
        """Set break mode - track break start time"""
        # Only set break start time if not already in break mode
        if not self.break_start_time:
            self.break_start_time = datetime.datetime.now().time()
            dialog = MDDialog(title="Break Mode", text="Break mode activated. Timer paused.")
            dialog.open()
        else:
            dialog = MDDialog(title="Info", text="Already in break mode.")
            dialog.open()

    def set_active_mode(self, instance):
        """Set active mode - calculate break duration and resume timer"""
        if self.break_start_time:
            # Calculate break duration
            break_end_time = datetime.datetime.now().time()
            today = datetime.date.today()

            break_start_dt = datetime.datetime.combine(today, self.break_start_time)
            break_end_dt = datetime.datetime.combine(today, break_end_time)

            # Handle case where break spans midnight
            if break_end_dt < break_start_dt:
                break_end_dt = break_end_dt + datetime.timedelta(days=1)

            break_duration = (break_end_dt - break_start_dt).total_seconds() / 60
            self.total_break_time += break_duration
            self.break_duration_db = self.total_break_time  # Store for database

            # Reset break start time
            self.break_start_time = None

            dialog = MDDialog(title="Active Mode", text=f"Active mode resumed. Break duration: {break_duration:.2f} minutes.")
            dialog.open()
        else:
            # Set start time if not already set
            if not self.start_time:
                self.start_time = datetime.datetime.now().time()
                dialog = MDDialog(title="Timer Started", text="Timer started for new task.")
                dialog.open()
            else:
                dialog = MDDialog(title="Info", text="Timer is already active.")
                dialog.open()

    def submit_task(self, instance):
        """Show preview popup before submitting task data"""
        # Collect main form data only
        main_data = {}
        for field in fields:
            if field in self.entries:
                entry = self.entries[field]
                if hasattr(entry, 'text'):
                    main_data[field] = entry.text
                else:
                    main_data[field] = ""
            else:
                main_data[field] = ""

        # Show preview dialog with just main form
        self.show_preview_dialog(main_data)

    def show_preview_dialog(self, form_data):
        """Show preview dialog for main form data"""
        # Create dialog content
        preview_content = MDBoxLayout(orientation="vertical", spacing=dp(10),
                                    adaptive_height=True, size_hint_y=None)
        preview_content.bind(minimum_height=preview_content.setter('height'))

        # Add form title
        title_label = MDLabel(text="Main Form", font_style="H6", size_hint_y=None, height=dp(40), bold=True)
        preview_content.add_widget(title_label)

        # Add form fields
        fields_added = 0
        for field, value in form_data.items():
            # Show all fields, but handle empty/default values appropriately
            display_value = str(value) if value else ""

            # Skip fields with default dropdown text that indicates no selection
            if value and not any(default_text in str(value) for default_text in [
                "Select Offer Type", "Select Business Line", "Select Content Type",
                "Select Group Assigned", "Select Ticket Status", "Select Issue Type", "Select Issue Subtype"
            ]):
                field_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(30))
                field_label = MDLabel(text=f"{field}:", size_hint_x=0.3, font_style="Subtitle2")
                value_label = MDLabel(text=display_value, size_hint_x=0.7, font_style="Body2")
                field_layout.add_widget(field_label)
                field_layout.add_widget(value_label)
                preview_content.add_widget(field_layout)
                fields_added += 1

        # Add message if no fields have values
        if fields_added == 0:
            no_data_label = MDLabel(text="No form data to preview. Please fill in some fields before submitting.",
                                  theme_text_color="Secondary", font_style="Body1",
                                  size_hint_y=None, height=dp(40))
            preview_content.add_widget(no_data_label)

        # Create scroll view for preview content
        preview_scroll = MDScrollView(size_hint=(1, 1))
        preview_scroll.add_widget(preview_content)

        # Store form data for submission
        self.main_form_data = form_data

        # Create dialog
        self.preview_dialog = MDDialog(
            title="Submit Preview",
            type="custom",
            content_cls=preview_scroll,
            size_hint=(0.9, 0.9),
            buttons=[
                MDFlatButton(text="Cancel", on_release=self.close_preview_dialog),
                MDRaisedButton(text="Confirm Submit", on_release=self.confirm_submit)
            ]
        )

        self.preview_dialog.open()

    def close_preview_dialog(self, instance):
        """Close preview dialog"""
        self.preview_dialog.dismiss()

    def confirm_submit(self, instance):
        """Confirm and submit main form data"""
        self.preview_dialog.dismiss()

        # Submit main form data
        self.submit_data(self.main_form_data)

        dialog = MDDialog(title="Success", text="Data submitted successfully!")
        dialog.open()

    def clear_form(self):
        """Clear all form fields"""
        # Clear main form
        for field, entry in self.entries.items():
            if hasattr(entry, 'text'):
                if field == "Offer Type":
                    entry.text = "Select Offer Type"
                elif field == "Business Line":
                    entry.text = "Select Business Line"
                elif field == "Content Type":
                    entry.text = "Select Content Type"
                elif field == "Group Assigned":
                    entry.text = "Select Group Assigned"
                elif field == "Ticket Status":
                    entry.text = "Select Ticket Status"
                elif field in ["Issue Type 1", "Issue Type 2", "Issue Type 3"]:
                    entry.text = f"Select {field}"
                elif field in ["Issue Subtype 1", "Issue Subtype 2", "Issue Subtype 3"]:
                    entry.text = f"Select {field}"
                else:
                    entry.text = ""

        # Reset selections
        if hasattr(self, 'selected_offers'):
            self.selected_offers.clear()

        # Reset processed ticket IDs when clearing form
        self.processed_ticket_ids.clear()
        self.titles_processed = 0
        self.titles_display.text = "Titles Processed: 0"



    def auto_save_data(self):
        """Auto-save current form data"""
        try:
            # Collect form data
            form_data = {}
            for field, entry in self.entries.items():
                if hasattr(entry, 'text'):
                    form_data[field] = entry.text
                else:
                    form_data[field] = ""

            # Save state data
            state_data = {
                "form_data": form_data,
                "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                "cumulative_time": self.cumulative_time,
                "titles_processed": self.titles_processed,
                "processed_ticket_ids": list(self.processed_ticket_ids)
            }

            # Save to general autosave file
            with open(self.autosave_path, 'wb') as f:
                pickle.dump(state_data, f)

            # Also save by Ticket ID if available
            ticket_id = form_data.get("Ticket ID", "").strip()
            if ticket_id:
                self.save_data_by_ticket_id(ticket_id, state_data)

            print("Auto-saved form data")
        except Exception as e:
            print(f"Auto-save error: {e}")

    def save_data_by_ticket_id(self, ticket_id, state_data):
        """Save form data by specific Ticket ID"""
        try:
            # Create a directory for ticket-specific autosaves if it doesn't exist
            ticket_autosave_dir = os.path.join(os.path.expanduser("~"), ".tv_series_tool_ticket_autosaves")
            if not os.path.exists(ticket_autosave_dir):
                os.makedirs(ticket_autosave_dir)

            # Create filename based on ticket ID (sanitize for filesystem)
            safe_ticket_id = "".join(c for c in ticket_id if c.isalnum() or c in ('-', '_')).rstrip()
            ticket_autosave_path = os.path.join(ticket_autosave_dir, f"ticket_{safe_ticket_id}.pkl")

            # Save ticket-specific data
            with open(ticket_autosave_path, 'wb') as f:
                pickle.dump(state_data, f)

            print(f"Auto-saved data for Ticket ID: {ticket_id}")
        except Exception as e:
            print(f"Ticket-specific auto-save error: {e}")

    def load_autosaved_data_by_ticket_id(self, ticket_id):
        """Load auto-saved data for a specific Ticket ID"""
        try:
            # Create path for ticket-specific autosave
            ticket_autosave_dir = os.path.join(os.path.expanduser("~"), ".tv_series_tool_ticket_autosaves")
            safe_ticket_id = "".join(c for c in ticket_id if c.isalnum() or c in ('-', '_')).rstrip()
            ticket_autosave_path = os.path.join(ticket_autosave_dir, f"ticket_{safe_ticket_id}.pkl")

            if os.path.exists(ticket_autosave_path):
                with open(ticket_autosave_path, 'rb') as f:
                    state_data = pickle.load(f)

                # Restore form data
                form_data = state_data.get("form_data", {})
                for field, value in form_data.items():
                    if field in self.entries and hasattr(self.entries[field], 'text'):
                        self.entries[field].text = value

                # Restore other state
                self.cumulative_time = state_data.get("cumulative_time", 0.0)
                self.titles_processed = state_data.get("titles_processed", 0)
                self.processed_ticket_ids = set(state_data.get("processed_ticket_ids", []))

                # Update displays
                self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
                self.titles_display.text = f"Titles Processed: {self.titles_processed}"

                # Restore start time
                start_time_str = state_data.get("start_time")
                if start_time_str:
                    self.start_time = datetime.datetime.strptime(start_time_str, "%H:%M:%S").time()

                dialog = MDDialog(title="Data Loaded", text=f"Auto-saved data loaded for Ticket ID: {ticket_id}")
                dialog.open()
                print(f"Auto-saved data loaded for Ticket ID: {ticket_id}")
                return True
            else:
                dialog = MDDialog(title="No Data Found", text=f"No auto-saved data found for Ticket ID: {ticket_id}")
                dialog.open()
                print(f"No auto-saved data found for Ticket ID: {ticket_id}")
                return False

        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to load auto-saved data for Ticket ID {ticket_id}: {str(e)}")
            dialog.open()
            print(f"Load error for Ticket ID {ticket_id}: {e}")
            return False

    def cleanup_ticket_autosave(self, ticket_id):
        """Clean up ticket-specific autosave file after successful submission"""
        try:
            ticket_autosave_dir = os.path.join(os.path.expanduser("~"), ".tv_series_tool_ticket_autosaves")
            safe_ticket_id = "".join(c for c in ticket_id if c.isalnum() or c in ('-', '_')).rstrip()
            ticket_autosave_path = os.path.join(ticket_autosave_dir, f"ticket_{safe_ticket_id}.pkl")

            if os.path.exists(ticket_autosave_path):
                os.remove(ticket_autosave_path)
                print(f"Cleaned up autosave file for Ticket ID: {ticket_id}")
        except Exception as e:
            print(f"Cleanup error for Ticket ID {ticket_id}: {e}")

    def try_load_autosave(self):
        """Try to load auto-saved data"""
        try:
            if os.path.exists(self.autosave_path):
                with open(self.autosave_path, 'rb') as f:
                    self._autosave_data = pickle.load(f)
                print("Auto-save data found")
            else:
                print("No auto-save data found")
        except Exception as e:
            print(f"Auto-save load error: {e}")

    def restore_autosaved_data(self):
        """Restore auto-saved data"""
        try:
            if hasattr(self, '_autosave_data'):
                state_data = self._autosave_data

                # Restore form data
                form_data = state_data.get("form_data", {})
                for field, value in form_data.items():
                    if field in self.entries and hasattr(self.entries[field], 'text'):
                        self.entries[field].text = value

                # Restore other state
                self.cumulative_time = state_data.get("cumulative_time", 0.0)
                self.titles_processed = state_data.get("titles_processed", 0)
                self.processed_ticket_ids = set(state_data.get("processed_ticket_ids", []))

                # Update displays
                self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
                self.titles_display.text = f"Titles Processed: {self.titles_processed}"

                # Restore start time
                start_time_str = state_data.get("start_time")
                if start_time_str:
                    self.start_time = datetime.datetime.strptime(start_time_str, "%H:%M:%S").time()

                print("Auto-saved data restored")
                return True
        except Exception as e:
            print(f"Restore error: {e}")
        return False

if __name__ == "__main__":
    TicketingForm().run()
